<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>批量字幕下载测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #FF0000;
            text-align: center;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        .url-list {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
            line-height: 1.5;
            white-space: pre-wrap;
        }
        .step {
            margin: 10px 0;
            padding: 10px;
            background-color: #f8f9fa;
            border-left: 4px solid #007bff;
        }
        .note {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎬 批量字幕下载测试</h1>
        
        <div class="test-section">
            <h3>📋 测试用YouTube视频URL列表</h3>
            <p>复制以下URL列表到插件的批量处理模式中：</p>
            <div class="url-list">https://www.youtube.com/watch?v=dQw4w9WgXcQ,
https://www.youtube.com/watch?v=jNQXAC9IVRw,
https://www.youtube.com/watch?v=9bZkp7q19f0,
https://www.youtube.com/watch?v=kJQP7kiw5Fk,
https://www.youtube.com/watch?v=fJ9rUzIMcZQ</div>
        </div>

        <div class="test-section">
            <h3>🧪 批量功能测试步骤</h3>
            
            <div class="step">
                <strong>步骤1：</strong> 重新加载Chrome插件
                <br>打开 chrome://extensions/ → 点击插件刷新按钮
            </div>
            
            <div class="step">
                <strong>步骤2：</strong> 打开插件并切换到批量模式
                <br>点击插件图标 → 点击"批量处理"标签页
            </div>
            
            <div class="step">
                <strong>步骤3：</strong> 输入测试URL
                <br>将上面的URL列表复制粘贴到文本框中
            </div>
            
            <div class="step">
                <strong>步骤4：</strong> 测试批量复制
                <br>选择格式 → 点击"批量复制" → 检查剪贴板内容
            </div>
            
            <div class="step">
                <strong>步骤5：</strong> 测试批量下载
                <br>选择格式 → 点击"批量下载" → 检查下载的合并文件
            </div>
        </div>

        <div class="test-section">
            <h3>✅ 预期结果</h3>
            
            <div class="step success">
                <strong>批量复制：</strong>
                <br>• 剪贴板中应包含所有视频的字幕内容
                <br>• 每个视频的字幕用 "=== 视频ID: xxx ===" 分隔
                <br>• 内容应该是可读的文本，无乱码
            </div>
            
            <div class="step success">
                <strong>批量下载：</strong>
                <br>• 下载一个合并的字幕文件
                <br>• 文件名格式：youtube_subtitles_Nvideos_时间戳.格式
                <br>• 文件内容与复制的内容一致
                <br>• 文件编码正确，无乱码
            </div>
            
            <div class="step success">
                <strong>进度显示：</strong>
                <br>• 进度条正常显示处理进度
                <br>• 结果列表显示每个视频的处理状态
                <br>• 成功/失败状态正确标识
            </div>
        </div>

        <div class="test-section">
            <h3>🔧 问题排查</h3>
            
            <div class="note">
                <strong>如果批量下载文件为空：</strong>
                <br>1. 检查浏览器控制台是否有错误
                <br>2. 先测试批量复制功能是否正常
                <br>3. 检查background.js的downloadCombinedSubtitles方法
            </div>
            
            <div class="note">
                <strong>如果单个下载有乱码：</strong>
                <br>1. 检查downloadFile方法的编码设置
                <br>2. 确认使用UTF-8编码的data URL
                <br>3. 测试不同格式（TXT/SRT/VTT）
            </div>
            
            <div class="note">
                <strong>调试方法：</strong>
                <br>1. 打开Chrome开发者工具
                <br>2. 查看Console标签页的日志输出
                <br>3. 检查Network标签页的请求状态
                <br>4. 在background.js中查看service worker日志
            </div>
        </div>

        <div class="test-section">
            <h3>📊 测试清单</h3>
            <div class="step">
                <input type="checkbox" id="test1"> 
                <label for="test1">插件重新加载成功</label>
            </div>
            <div class="step">
                <input type="checkbox" id="test2"> 
                <label for="test2">批量处理标签页正常显示</label>
            </div>
            <div class="step">
                <input type="checkbox" id="test3"> 
                <label for="test3">URL输入和解析正常</label>
            </div>
            <div class="step">
                <input type="checkbox" id="test4"> 
                <label for="test4">批量复制功能正常</label>
            </div>
            <div class="step">
                <input type="checkbox" id="test5"> 
                <label for="test5">批量下载生成合并文件</label>
            </div>
            <div class="step">
                <input type="checkbox" id="test6"> 
                <label for="test6">下载文件内容正确无乱码</label>
            </div>
            <div class="step">
                <input type="checkbox" id="test7"> 
                <label for="test7">进度条和状态显示正常</label>
            </div>
        </div>
    </div>

    <script>
        // 简单的测试进度跟踪
        document.querySelectorAll('input[type="checkbox"]').forEach(checkbox => {
            checkbox.addEventListener('change', function() {
                const completed = document.querySelectorAll('input[type="checkbox"]:checked').length;
                const total = document.querySelectorAll('input[type="checkbox"]').length;
                console.log(`批量功能测试进度: ${completed}/${total}`);
                
                if (completed === total) {
                    alert('🎉 恭喜！批量功能测试全部完成！');
                }
            });
        });
    </script>
</body>
</html>
