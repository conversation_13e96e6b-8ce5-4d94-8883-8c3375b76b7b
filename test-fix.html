<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>字幕获取修复测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        input[type="text"] {
            width: 100%;
            padding: 8px;
            margin: 5px 0;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        button {
            background-color: #4CAF50;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #45a049;
        }
        button:disabled {
            background-color: #cccccc;
            cursor: not-allowed;
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            border-radius: 4px;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 12px;
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .info {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        .subtitle-list {
            max-height: 200px;
            overflow-y: auto;
            border: 1px solid #ddd;
            padding: 10px;
            margin-top: 10px;
        }
        .subtitle-item {
            padding: 5px;
            margin: 2px 0;
            background-color: #f8f9fa;
            border-radius: 3px;
            cursor: pointer;
        }
        .subtitle-item:hover {
            background-color: #e9ecef;
        }
        .subtitle-item.selected {
            background-color: #007bff;
            color: white;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>YouTube字幕获取修复测试</h1>
        <p>此页面用于测试基于youtube-transcript-api逻辑的字幕获取修复是否有效。</p>

        <div class="test-section">
            <h3>1. 测试视频ID提取</h3>
            <input type="text" id="videoUrl" placeholder="输入YouTube视频URL" 
                   value="https://www.youtube.com/watch?v=dQw4w9WgXcQ">
            <button onclick="testVideoIdExtraction()">提取视频ID</button>
            <div id="videoIdResult" class="result"></div>
        </div>

        <div class="test-section">
            <h3>2. 测试字幕列表获取</h3>
            <input type="text" id="testVideoId" placeholder="输入视频ID" value="dQw4w9WgXcQ">
            <button onclick="testSubtitlesList()" id="testSubtitlesBtn">获取字幕列表</button>
            <div id="subtitlesResult" class="result"></div>
            <div id="subtitlesList" class="subtitle-list" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>3. 测试字幕内容获取</h3>
            <p>先获取字幕列表，然后点击字幕项目来测试内容获取</p>
            <button onclick="testSubtitleContent()" id="testContentBtn" disabled>获取选中字幕内容</button>
            <div id="contentResult" class="result"></div>
        </div>

        <div class="test-section">
            <h3>4. 测试说明</h3>
            <div class="info">
                <strong>修复要点：</strong><br>
                1. 使用YouTube内部API (youtubei/v1/player)<br>
                2. 正确的客户端配置 (ANDROID)<br>
                3. 完整的错误处理<br>
                4. 降级到HTML解析方案<br><br>
                
                <strong>测试步骤：</strong><br>
                1. 输入YouTube视频URL测试ID提取<br>
                2. 使用提取的ID获取字幕列表<br>
                3. 点击字幕项目测试内容获取<br>
                4. 检查控制台日志查看详细信息
            </div>
        </div>
    </div>

    <script>
        let selectedSubtitle = null;

        // 模拟background.js中的YouTubeSubtitleService类
        class TestYouTubeSubtitleService {
            // 提取视频ID
            extractVideoId(url) {
                const urlParams = new URLSearchParams(new URL(url).search);
                return urlParams.get('v');
            }

            // 获取视频HTML页面
            async fetchVideoHTML(videoId) {
                const watchUrl = `https://www.youtube.com/watch?v=${videoId}`;
                const response = await fetch(watchUrl, {
                    headers: {
                        'Accept-Language': 'en-US,en;q=0.9',
                        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
                    }
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                return await response.text();
            }

            // 从HTML中提取Innertube API密钥
            extractInnertubeApiKey(html, videoId) {
                const pattern = /"INNERTUBE_API_KEY":\s*"([a-zA-Z0-9_-]+)"/;
                const match = html.match(pattern);
                
                if (match && match[1]) {
                    return match[1];
                }
                
                if (html.includes('class="g-recaptcha"')) {
                    throw new Error('IP被阻止，需要验证码');
                }
                
                throw new Error('无法解析YouTube数据，找不到API密钥');
            }

            // 调用YouTube内部API
            async fetchInnertubeData(videoId, apiKey) {
                const url = `https://www.youtube.com/youtubei/v1/player?key=${apiKey}`;
                const payload = {
                    context: {
                        client: {
                            clientName: "ANDROID",
                            clientVersion: "20.10.38"
                        }
                    },
                    videoId: videoId
                };

                const response = await fetch(url, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept-Language': 'en-US,en;q=0.9',
                        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
                    },
                    body: JSON.stringify(payload)
                });

                if (!response.ok) {
                    throw new Error(`YouTube API请求失败: ${response.status} ${response.statusText}`);
                }

                return await response.json();
            }

            // 从Innertube数据中提取字幕信息
            extractCaptionsJson(innertubeData, videoId) {
                // 检查播放状态
                const playabilityStatus = innertubeData.playabilityStatus;
                if (!playabilityStatus) {
                    throw new Error('无法获取视频播放状态');
                }

                if (playabilityStatus.status !== 'OK') {
                    const reason = playabilityStatus.reason || '未知错误';
                    if (playabilityStatus.status === 'LOGIN_REQUIRED') {
                        throw new Error('需要登录才能访问此视频');
                    } else if (playabilityStatus.status === 'ERROR') {
                        throw new Error(`视频不可用: ${reason}`);
                    } else {
                        throw new Error(`视频播放失败: ${reason}`);
                    }
                }

                // 提取字幕数据
                const captions = innertubeData.captions;
                if (!captions || !captions.playerCaptionsTracklistRenderer) {
                    throw new Error('此视频没有可用的字幕');
                }

                const captionsJson = captions.playerCaptionsTracklistRenderer;
                if (!captionsJson.captionTracks || captionsJson.captionTracks.length === 0) {
                    throw new Error('此视频没有可用的字幕轨道');
                }

                return captionsJson;
            }

            // 构建字幕列表
            buildSubtitlesList(captionsJson) {
                const subtitles = [];

                for (const caption of captionsJson.captionTracks) {
                    const subtitle = {
                        name: caption.name?.runs?.[0]?.text || caption.languageCode,
                        languageCode: caption.languageCode,
                        url: caption.baseUrl.replace('&fmt=srv3', ''), // 移除srv3格式参数
                        kind: caption.kind || 'captions',
                        isGenerated: caption.kind === 'asr' // ASR表示自动生成
                    };
                    
                    subtitles.push(subtitle);
                }

                return subtitles;
            }

            // 获取字幕内容
            async fetchSubtitleContent(url) {
                try {
                    const response = await fetch(url);
                    return await response.text();
                } catch (error) {
                    throw new Error('获取字幕内容失败：' + error.message);
                }
            }

            // 主要的字幕列表获取方法
            async fetchSubtitlesList(videoId) {
                try {
                    // 使用youtube-transcript-api的方法：获取HTML → 提取API密钥 → 调用内部API
                    const html = await this.fetchVideoHTML(videoId);
                    const apiKey = this.extractInnertubeApiKey(html, videoId);
                    const innertubeData = await this.fetchInnertubeData(videoId, apiKey);
                    const captionsJson = this.extractCaptionsJson(innertubeData, videoId);
                    
                    const subtitles = this.buildSubtitlesList(captionsJson);
                    return { success: true, subtitles };
                } catch (error) {
                    console.error("获取字幕列表失败:", error);
                    return { success: false, error: error.message };
                }
            }
        }

        const testService = new TestYouTubeSubtitleService();

        function testVideoIdExtraction() {
            const url = document.getElementById('videoUrl').value;
            const resultDiv = document.getElementById('videoIdResult');
            
            try {
                const videoId = testService.extractVideoId(url);
                resultDiv.className = 'result success';
                resultDiv.textContent = `提取成功！视频ID: ${videoId}`;
                
                // 自动填充到测试字幕列表的输入框
                document.getElementById('testVideoId').value = videoId;
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `提取失败: ${error.message}`;
            }
        }

        async function testSubtitlesList() {
            const videoId = document.getElementById('testVideoId').value;
            const resultDiv = document.getElementById('subtitlesResult');
            const listDiv = document.getElementById('subtitlesList');
            const btn = document.getElementById('testSubtitlesBtn');
            
            if (!videoId) {
                resultDiv.className = 'result error';
                resultDiv.textContent = '请输入视频ID';
                return;
            }

            btn.disabled = true;
            btn.textContent = '获取中...';
            resultDiv.className = 'result info';
            resultDiv.textContent = '正在获取字幕列表...';

            try {
                const result = await testService.fetchSubtitlesList(videoId);
                
                if (result.success) {
                    resultDiv.className = 'result success';
                    resultDiv.textContent = `获取成功！找到 ${result.subtitles.length} 个字幕轨道`;
                    
                    // 显示字幕列表
                    listDiv.style.display = 'block';
                    listDiv.innerHTML = '';
                    
                    result.subtitles.forEach((subtitle, index) => {
                        const item = document.createElement('div');
                        item.className = 'subtitle-item';
                        item.textContent = `${subtitle.name} (${subtitle.languageCode}) ${subtitle.isGenerated ? '[自动生成]' : '[手动创建]'}`;
                        item.onclick = () => selectSubtitle(subtitle, item);
                        listDiv.appendChild(item);
                    });
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = `获取失败: ${result.error}`;
                    listDiv.style.display = 'none';
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `获取失败: ${error.message}`;
                listDiv.style.display = 'none';
            } finally {
                btn.disabled = false;
                btn.textContent = '获取字幕列表';
            }
        }

        function selectSubtitle(subtitle, element) {
            // 清除之前的选择
            document.querySelectorAll('.subtitle-item').forEach(item => {
                item.classList.remove('selected');
            });
            
            // 选择当前项目
            element.classList.add('selected');
            selectedSubtitle = subtitle;
            
            // 启用内容测试按钮
            document.getElementById('testContentBtn').disabled = false;
        }

        async function testSubtitleContent() {
            if (!selectedSubtitle) {
                alert('请先选择一个字幕');
                return;
            }

            const resultDiv = document.getElementById('contentResult');
            const btn = document.getElementById('testContentBtn');
            
            btn.disabled = true;
            btn.textContent = '获取中...';
            resultDiv.className = 'result info';
            resultDiv.textContent = '正在获取字幕内容...';

            try {
                const content = await testService.fetchSubtitleContent(selectedSubtitle.url);
                
                resultDiv.className = 'result success';
                resultDiv.textContent = `获取成功！内容长度: ${content.length} 字符\n\n内容预览:\n${content.substring(0, 500)}${content.length > 500 ? '...' : ''}`;
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `获取失败: ${error.message}`;
            } finally {
                btn.disabled = false;
                btn.textContent = '获取选中字幕内容';
            }
        }
    </script>
</body>
</html>
