# YouTube字幕下载器 - 操作指南

## 开发环境设置

### 1. 项目初始化
```bash
# 创建项目目录
mkdir youtube-subtitle
cd youtube-subtitle

# 项目文件已创建完成，包括：
# - manifest.json
# - popup.html
# - popup.js
# - content.js
# - background.js
# - styles.css
```

### 2. 创建图标文件
需要创建以下尺寸的图标文件：
```bash
mkdir icons
# 需要添加以下图标文件：
# - icons/icon16.png (16x16像素)
# - icons/icon48.png (48x48像素)
# - icons/icon128.png (128x128像素)
```

## Chrome插件安装和测试

### 1. 开发者模式安装
1. 打开Chrome浏览器
2. 访问 `chrome://extensions/`
3. 开启右上角的"开发者模式"
4. 点击"加载已解压的扩展程序"
5. 选择项目文件夹 `youtube-subtitle`
6. 插件安装完成

### 2. 功能测试步骤

#### 测试1: 基本功能测试
1. 打开任意YouTube视频页面
2. 点击浏览器工具栏中的插件图标
3. 检查是否正确显示视频信息
4. 验证字幕列表是否正确加载

#### 测试2: 字幕下载测试
1. 在有字幕的YouTube视频页面使用插件
2. 选择一个可用的字幕语言
3. 选择下载格式（SRT/VTT/TXT）
4. 点击"下载字幕"按钮
5. 验证文件是否正确下载

#### 测试3: 字幕复制测试
1. 在有字幕的YouTube视频页面使用插件
2. 选择一个可用的字幕语言
3. 选择复制格式（SRT/VTT/TXT）
4. 点击"复制字幕"按钮
5. 验证字幕内容是否复制到剪贴板
6. 检查浏览器控制台的调试信息

#### 测试4: 批量处理测试
1. 切换到"批量处理"标签页
2. 输入多个YouTube URL（逗号分隔）
3. 测试批量复制功能
4. 测试批量下载功能（生成合并文件）
5. 检查进度条和结果显示

#### 测试5: 错误处理测试
1. 在非YouTube页面使用插件，检查错误提示
2. 在无字幕的视频页面测试，检查提示信息
3. 测试网络异常情况下的错误处理

## 开发调试

### 1. 调试popup界面
1. 右键点击插件图标
2. 选择"检查弹出式窗口"
3. 在开发者工具中调试popup.js

### 2. 调试content script
1. 在YouTube页面按F12打开开发者工具
2. 在Console中查看content.js的日志
3. 在Sources标签中设置断点调试

### 3. 调试background script
1. 访问 `chrome://extensions/`
2. 找到插件，点击"检查视图 service worker"
3. 在开发者工具中调试background.js

### 4. 查看插件日志
```javascript
// 在各个脚本中添加调试日志
console.log('调试信息:', data);
console.error('错误信息:', error);
```

## 常见问题解决

### 1. 插件无法加载
**问题**: 插件安装后无法正常工作
**解决方案**:
- 检查manifest.json语法是否正确
- 确认所有文件路径正确
- 查看Chrome扩展程序页面的错误信息

### 2. 无法获取视频信息
**问题**: 在YouTube页面无法获取视频信息
**解决方案**:
- 刷新YouTube页面后重试
- 检查content.js是否正确注入
- 确认页面URL匹配规则正确

### 3. 字幕获取失败
**问题**: 无法获取字幕列表或内容
**解决方案**:
- 检查网络连接
- 确认视频确实有字幕
- 查看background.js中的错误日志

### 5. 字幕内容为空
**问题**: 下载的字幕文件为空或复制的内容为空
**解决方案**:
- 检查浏览器控制台的调试信息
- 确认原始字幕内容是否获取成功
- 检查格式转换是否正确（支持XML和VTT格式）
- 使用复制功能查看实际字幕内容

### 6. 自动获取视频链接失败
**问题**: 点击"自动获取当前页面视频"按钮报错或获取不到链接
**解决方案**:
- 确认在YouTube页面（搜索页面或首页）
- 检查页面是否完全加载
- 在控制台运行debug-youtube.js脚本分析页面结构
- 查看控制台的错误信息和警告
- YouTube更新页面结构时需要更新选择器

### 4. 下载功能异常
**问题**: 点击下载按钮无反应或报错"URL.createObjectURL is not a function"
**解决方案**:
- 检查downloads权限是否正确配置
- 确认浏览器允许下载
- 查看background.js中的下载逻辑
- 如果出现URL.createObjectURL错误，说明需要使用data URL方式下载（已修复）

## 代码修改和更新

### 1. 修改插件后重新加载
1. 在 `chrome://extensions/` 页面
2. 找到插件，点击刷新按钮
3. 或者先移除插件再重新加载

### 2. 版本更新
修改manifest.json中的version字段：
```json
{
  "version": "1.0.1"
}
```

### 3. 添加新功能
1. 修改相应的JavaScript文件
2. 如需新权限，更新manifest.json
3. 重新加载插件进行测试

## 发布准备

### 1. 代码优化
- 移除所有console.log调试语句
- 压缩CSS和JavaScript文件
- 优化图标文件大小

### 2. 测试完整性
- 在不同版本的Chrome浏览器测试
- 测试各种YouTube页面类型
- 验证所有错误处理场景

### 3. 打包发布
1. 创建插件zip包：
```bash
zip -r youtube-subtitle-extension.zip . -x "*.md" "*.git*"
```

2. 准备Chrome Web Store发布材料：
   - 插件描述
   - 截图和宣传图片
   - 隐私政策（如需要）

## 维护和更新

### 1. 监控YouTube API变化
- YouTube页面结构可能会变化
- 需要定期测试和更新字幕获取逻辑

### 2. 用户反馈处理
- 收集用户使用问题
- 根据反馈优化功能

### 3. 安全更新
- 定期检查依赖安全性
- 更新Chrome API使用方式

## 性能优化建议

### 1. 减少内存使用
- 及时清理不需要的对象引用
- 优化字幕数据处理逻辑

### 2. 提升响应速度
- 缓存常用数据
- 异步处理耗时操作

### 3. 网络请求优化
- 减少不必要的网络请求
- 添加请求超时处理
