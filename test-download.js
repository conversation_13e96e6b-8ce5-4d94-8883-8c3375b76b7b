// 测试下载功能的脚本
// 可以在Chrome开发者工具的Console中运行

function testDownloadFunction() {
    // 模拟字幕内容
    const testContent = `WEBVTT

00:00:01.000 --> 00:00:05.000
这是第一行字幕测试内容

00:00:06.000 --> 00:00:10.000
这是第二行字幕测试内容

00:00:11.000 --> 00:00:15.000
This is English subtitle content
`;

    // 测试base64编码
    try {
        const base64Content = btoa(unescape(encodeURIComponent(testContent)));
        const dataUrl = `data:text/plain;charset=utf-8;base64,${base64Content}`;
        
        console.log('✅ Base64编码成功');
        console.log('Data URL长度:', dataUrl.length);
        console.log('Data URL前缀:', dataUrl.substring(0, 50) + '...');
        
        // 测试下载（需要在扩展环境中运行）
        if (typeof chrome !== 'undefined' && chrome.downloads) {
            chrome.downloads.download({
                url: dataUrl,
                filename: 'test_subtitle.txt',
                saveAs: true
            }).then(() => {
                console.log('✅ 下载测试成功');
            }).catch(error => {
                console.error('❌ 下载测试失败:', error);
            });
        } else {
            console.log('⚠️ 非扩展环境，无法测试实际下载');
        }
        
    } catch (error) {
        console.error('❌ Base64编码失败:', error);
    }
}

// 测试格式转换
function testFormatConversion() {
    const vttContent = `WEBVTT

00:00:01.000 --> 00:00:05.000
第一行字幕

00:00:06.000 --> 00:00:10.000
第二行字幕
`;

    // 测试VTT到SRT转换
    function vttToSrt(vttContent) {
        let srtContent = '';
        let counter = 1;
        
        const lines = vttContent.split('\n');
        let startIndex = 0;
        
        for (let i = 0; i < lines.length; i++) {
            if (lines[i].includes('-->')) {
                startIndex = i;
                break;
            }
        }

        for (let i = startIndex; i < lines.length; i++) {
            const line = lines[i].trim();
            
            if (line.includes('-->')) {
                const timeRange = line.replace(/\./g, ',');
                srtContent += `${counter}\n${timeRange}\n`;
                
                let textLines = [];
                for (let j = i + 1; j < lines.length && lines[j].trim() !== ''; j++) {
                    if (!lines[j].includes('-->')) {
                        textLines.push(lines[j].trim());
                    }
                }
                
                srtContent += textLines.join('\n') + '\n\n';
                counter++;
            }
        }

        return srtContent;
    }

    const srtResult = vttToSrt(vttContent);
    console.log('✅ VTT到SRT转换测试:');
    console.log(srtResult);
}

// 运行测试
console.log('🧪 开始测试下载功能...');
testDownloadFunction();
testFormatConversion();
