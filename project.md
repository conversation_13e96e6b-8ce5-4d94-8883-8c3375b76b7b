# YouTube字幕下载器 Chrome插件

## 项目概述
这是一个Chrome浏览器插件，用于从YouTube视频页面下载字幕文件。支持多种字幕格式（SRT、VTT、TXT），提供用户友好的界面。

## 项目结构

```
youtube-subtitle/
├── manifest.json          # Chrome插件配置文件
├── popup.html             # 插件弹窗界面
├── popup.js               # 弹窗逻辑处理
├── content.js             # 内容脚本（在YouTube页面运行）
├── background.js          # 后台服务工作者
├── styles.css             # 样式文件
├── icons/                 # 图标文件夹
│   └── icon.svg          # SVG图标文件
├── project.md             # 项目文档（本文件）
├── 操作.md                # 操作说明文档
├── test.html              # 测试页面
└── README.md              # 项目说明
```

## 核心功能模块

### 1. Manifest配置 (manifest.json)
- **功能**: 定义插件的基本信息、权限和文件结构
- **关键权限**:
  - `activeTab`: 访问当前活动标签页
  - `storage`: 存储用户设置
  - `downloads`: 下载文件权限
  - `host_permissions`: 访问YouTube域名

### 2. 用户界面 (popup.html + styles.css)
- **功能**: 提供插件的主要交互界面
- **组件**:
  - 标签页切换（单个视频/批量处理）
  - 单个视频模式：视频信息、字幕列表、下载/复制按钮
  - 批量处理模式：URL输入框、进度条、结果显示
  - 状态提示和错误信息

### 3. 弹窗逻辑 (popup.js)
- **主要类**: `YouTubeSubtitleDownloader`
- **核心方法**:
  - `checkCurrentTab()`: 检查当前是否在YouTube视频页面
  - `loadSubtitles()`: 获取可用字幕列表
  - `selectSubtitle()`: 处理字幕选择
  - `downloadSubtitle()`: 执行字幕下载
  - `copySubtitle()`: 复制字幕内容到剪贴板
  - `batchDownload()`: 批量下载字幕
  - `batchCopy()`: 批量复制字幕
  - `processBatch()`: 批量处理核心逻辑
  - `autoFillFromCurrentPage()`: 自动获取当前页面视频链接

### 4. 内容脚本 (content.js)
- **主要类**: `YouTubeContentScript`
- **功能**:
  - 从YouTube页面提取视频ID
  - 获取视频标题
  - 自动获取搜索页面/首页的视频链接
  - 与popup进行消息通信

### 5. 后台服务 (background.js)
- **主要类**: `YouTubeSubtitleService`
- **核心功能**:
  - 获取YouTube字幕列表
  - 获取字幕内容（用于复制和下载）
  - 格式转换（XML/VTT → SRT/VTT/TXT）
  - 单个文件下载处理
  - 批量文件下载处理
  - 调试信息输出

## 数据流程

### 1. 插件启动流程
```
用户点击插件图标 → popup.html加载 → popup.js初始化 → 检查当前标签页
```

### 2. 视频信息获取流程
```
popup.js → 发送消息到content.js → content.js提取视频信息 → 返回给popup.js
```

### 3. 字幕获取流程
```
popup.js → 发送消息到background.js → background.js请求YouTube API → 解析字幕列表 → 返回给popup.js
```

### 4. 字幕下载流程
```
用户选择字幕 → popup.js发送下载请求 → background.js获取字幕内容 → 格式转换 → 触发浏览器下载
```

### 5. 字幕复制流程
```
用户选择字幕 → popup.js发送复制请求 → background.js获取字幕内容 → 格式转换 → 返回内容 → 复制到剪贴板
```

### 6. 批量处理流程
```
用户输入URL列表 → 解析视频ID → 循环处理每个视频 → 获取英文字幕 → 合并所有字幕 → 批量下载/复制 → 显示处理结果
```

### 7. 自动获取视频链接流程
```
用户点击自动获取按钮 → popup.js获取当前标签页 → 发送消息到content.js → 解析页面视频链接 → 返回链接列表 → 自动填充到文本框
```

## 技术实现细节

### 1. 视频ID提取
- 从URL参数中提取`v`参数值
- 支持标准YouTube URL格式

### 2. 字幕获取方法
- **主要方法**: 解析YouTube页面HTML中的字幕配置
- **备用方法**: 使用第三方API（待实现）

### 3. 格式转换
- **自动格式检测**: 支持XML和VTT两种输入格式
- **XML → SRT/VTT/TXT**: 解析YouTube的XML字幕格式，转换时间轴和文本
- **VTT → SRT**: 转换时间格式（点号改为逗号），添加序号
- **VTT → TXT**: 提取纯文本内容，移除时间轴
- **HTML实体解码**: 自动处理&amp;#39;等HTML编码字符

### 4. 文件下载
- 使用Blob API创建文件对象
- 通过Chrome Downloads API触发下载
- 自动清理临时URL对象

## 消息通信架构

### popup.js ↔ content.js
```javascript
// popup.js发送
chrome.tabs.sendMessage(tabId, { action: 'getVideoInfo' })

// content.js响应
{ success: true, data: { videoId, title, url } }
```

### popup.js ↔ background.js
```javascript
// 获取字幕列表
chrome.runtime.sendMessage({ action: 'getSubtitles', videoId })

// 下载字幕
chrome.runtime.sendMessage({
  action: 'downloadSubtitle',
  subtitle, videoId, videoTitle, format
})
```

## 错误处理机制

### 1. 页面检测错误
- 非YouTube页面提示
- 无法连接到页面提示

### 2. 字幕获取错误
- 网络请求失败
- 解析字幕信息失败
- 无可用字幕提示

### 3. 下载错误
- 字幕内容获取失败
- 格式转换错误
- 文件下载失败

## 扩展功能规划

### 1. 多语言支持
- 界面多语言
- 字幕语言自动识别

### 2. 批量下载
- 支持下载所有可用语言字幕
- 支持批量格式转换

### 3. 自定义设置
- 默认下载格式设置
- 文件命名规则自定义

### 4. 字幕预览
- 在下载前预览字幕内容
- 支持字幕时间轴调整

## 安全考虑

### 1. 权限最小化
- 仅请求必要的浏览器权限
- 限制访问域名范围

### 2. 数据处理
- 不存储用户个人信息
- 本地处理字幕数据

### 3. 网络请求
- 仅向YouTube官方域名发送请求
- 避免第三方数据泄露
