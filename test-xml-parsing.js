// 测试XML字幕解析功能（Service Worker兼容版本）
// 可以在Chrome开发者工具的Console中运行

function testXmlParsing() {
  // 模拟XML字幕内容
  const xmlContent = `<?xml version="1.0" encoding="utf-8" ?>
<transcript>
    <text start="0" dur="4.799">Imagine building an entire army of</text>
    <text start="2.56" dur="4">agents from just one prompt. In this</text>
    <text start="4.799" dur="4.8">video, I&amp;#39;m going to show you exactly how</text>
    <text start="6.56" dur="5.44">to do this using the new multi-agent</text>
</transcript>`

  console.log("🧪 测试XML字幕解析（Service Worker兼容版本）...")
  console.log("原始XML内容长度:", xmlContent.length)

  try {
    // 使用正则表达式解析XML（Service Worker兼容）
    const textElements = parseXmlText(xmlContent)

    console.log("✅ XML解析成功，找到", textElements.length, "个字幕片段")

    // 测试转换为SRT格式
    let srtContent = ""
    let counter = 1

    for (const element of textElements) {
      const start = parseFloat(element.start)
      const dur = parseFloat(element.dur)
      const end = start + dur
      const text = decodeHtmlEntities(element.text.trim())

      if (text) {
        const startTime = secondsToSrtTime(start)
        const endTime = secondsToSrtTime(end)

        srtContent += `${counter}\n${startTime} --> ${endTime}\n${text}\n\n`
        counter++
      }
    }

    console.log("✅ SRT转换成功")
    console.log("SRT内容长度:", srtContent.length)
    console.log("SRT内容预览:")
    console.log(srtContent.substring(0, 300))
  } catch (error) {
    console.error("❌ XML解析失败:", error)
  }
}

function parseXmlText(xmlContent) {
  const textElements = []
  // 使用正则表达式匹配<text>标签
  const textRegex =
    /<text\s+start="([^"]+)"\s+dur="([^"]+)"[^>]*>([^<]*)<\/text>/g
  let match

  while ((match = textRegex.exec(xmlContent)) !== null) {
    textElements.push({
      start: match[1],
      dur: match[2],
      text: match[3],
    })
  }

  return textElements
}

function secondsToSrtTime(seconds) {
  const hours = Math.floor(seconds / 3600)
  const minutes = Math.floor((seconds % 3600) / 60)
  const secs = Math.floor(seconds % 60)
  const ms = Math.floor((seconds % 1) * 1000)

  return `${hours.toString().padStart(2, "0")}:${minutes
    .toString()
    .padStart(2, "0")}:${secs.toString().padStart(2, "0")},${ms
    .toString()
    .padStart(3, "0")}`
}

function decodeHtmlEntities(text) {
  const textarea = document.createElement("textarea")
  textarea.innerHTML = text
  return textarea.value
}

// 运行测试
testXmlParsing()
