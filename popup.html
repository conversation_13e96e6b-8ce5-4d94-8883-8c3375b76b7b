<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>YouTube字幕下载器</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div class="container">
        <div class="header">
            <h2>YouTube字幕下载器</h2>
        </div>

        <div class="content">
            <!-- 标签页切换 -->
            <div class="tabs">
                <button id="singleTab" class="tab-button active">单个视频</button>
                <button id="batchTab" class="tab-button">批量处理</button>
            </div>

            <!-- 单个视频模式 -->
            <div id="singleMode" class="tab-content">
                <div id="status" class="status">
                    <span id="statusText">检测YouTube视频中...</span>
                </div>

            <div id="videoInfo" class="video-info" style="display: none;">
                <div class="video-title">
                    <strong>视频标题：</strong>
                    <span id="videoTitle"></span>
                </div>
                <div class="video-id">
                    <strong>视频ID：</strong>
                    <span id="videoId"></span>
                </div>
            </div>

            <div id="subtitlesList" class="subtitles-list" style="display: none;">
                <h3>可用字幕：</h3>
                <div id="subtitlesContainer"></div>
            </div>

            <div id="downloadSection" class="download-section" style="display: none;">
                <div class="format-selection">
                    <label for="formatSelect">下载格式：</label>
                    <select id="formatSelect">
                        <option value="srt">SRT</option>
                        <option value="vtt">VTT</option>
                        <option value="txt">TXT</option>
                    </select>
                </div>
                <div class="action-buttons">
                    <button id="downloadBtn" class="download-btn">下载字幕</button>
                    <button id="copyBtn" class="copy-btn">复制字幕</button>
                </div>
            </div>

                <div id="errorMessage" class="error-message" style="display: none;">
                    <span id="errorText"></span>
                </div>
            </div>

            <!-- 批量处理模式 -->
            <div id="batchMode" class="tab-content" style="display: none;">
                <div class="batch-input-section">
                    <div class="input-header">
                        <label for="videoUrls">YouTube视频URL列表（逗号分隔）：</label>
                        <button id="autoFillBtn" class="auto-fill-btn">自动获取当前页面视频</button>
                    </div>
                    <textarea id="videoUrls" placeholder="请输入YouTube视频URL，用逗号分隔&#10;例如：&#10;https://www.youtube.com/watch?v=dQw4w9WgXcQ,&#10;https://www.youtube.com/watch?v=jNQXAC9IVRw&#10;&#10;或点击上方按钮自动获取YouTube搜索页面的视频链接"></textarea>
                </div>

                <div class="batch-options">
                    <div class="format-selection">
                        <label for="batchFormatSelect">下载格式：</label>
                        <select id="batchFormatSelect">
                            <option value="srt">SRT</option>
                            <option value="vtt">VTT</option>
                            <option value="txt">TXT</option>
                        </select>
                    </div>
                    <div class="action-buttons">
                        <button id="batchDownloadBtn" class="download-btn">批量下载</button>
                        <button id="batchCopyBtn" class="copy-btn">批量复制</button>
                    </div>
                </div>

                <div id="batchProgress" class="batch-progress" style="display: none;">
                    <div class="progress-bar">
                        <div id="progressFill" class="progress-fill"></div>
                    </div>
                    <div id="progressText" class="progress-text">处理中... 0/0</div>
                </div>

                <div id="batchResults" class="batch-results" style="display: none;">
                    <h3>处理结果：</h3>
                    <div id="resultsContainer"></div>
                </div>

                <div id="batchErrorMessage" class="error-message" style="display: none;">
                    <span id="batchErrorText"></span>
                </div>
            </div>
        </div>

        <div class="footer">
            <p>在YouTube视频页面使用此插件</p>
        </div>
    </div>

    <script src="popup.js"></script>
</body>
</html>
