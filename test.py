from youtube_transcript_api import YouTubeTranscriptApi

# 替换为你要下载字幕的视频ID
video_id = 'L_FY6aW9cJ4' # 例如：<PERSON> - Never Gonna Give You Up

try:
    # 获取字幕，默认尝试英文
    transcript_list = YouTubeTranscriptApi.get_transcript(video_id)

    # 打印字幕内容
    print(f"--- 视频 '{video_id}' 的字幕 ---")
    for segment in transcript_list:
        print(f"[{segment['start']:.2f}s - {segment['duration']:.2f}s] {segment['text']}")

except Exception as e:
    print(f"获取字幕时发生错误: {e}")