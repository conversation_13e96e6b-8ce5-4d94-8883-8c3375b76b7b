<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>YouTube字幕下载器 - 测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #FF0000;
            text-align: center;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        .step {
            margin: 10px 0;
            padding: 10px;
            background-color: #f8f9fa;
            border-left: 4px solid #007bff;
        }
        .youtube-link {
            display: inline-block;
            padding: 10px 20px;
            background-color: #FF0000;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            margin: 10px 5px;
        }
        .youtube-link:hover {
            background-color: #CC0000;
        }
        .note {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎬 YouTube字幕下载器测试页面</h1>

        <div class="test-section">
            <h3>📋 安装步骤</h3>
            <div class="step">1. 打开Chrome浏览器，访问 chrome://extensions/</div>
            <div class="step">2. 开启右上角的"开发者模式"</div>
            <div class="step">3. 点击"加载已解压的扩展程序"</div>
            <div class="step">4. 选择项目文件夹</div>
            <div class="step">5. 插件安装完成，工具栏会出现插件图标</div>
        </div>

        <div class="test-section">
            <h3>🧪 测试用YouTube视频</h3>
            <p>点击以下链接打开测试视频，然后使用插件下载字幕：</p>

            <a href="https://www.youtube.com/watch?v=dQw4w9WgXcQ" class="youtube-link" target="_blank">
                测试视频1 (经典视频)
            </a>

            <a href="https://www.youtube.com/watch?v=jNQXAC9IVRw" class="youtube-link" target="_blank">
                测试视频2 (Me at the zoo)
            </a>

            <a href="https://www.youtube.com/watch?v=9bZkp7q19f0" class="youtube-link" target="_blank">
                测试视频3 (PSY - GANGNAM STYLE)
            </a>
        </div>

        <div class="test-section">
            <h3>✅ 功能测试清单</h3>
            <div class="step">
                <input type="checkbox" id="test1">
                <label for="test1">插件图标正确显示在工具栏</label>
            </div>
            <div class="step">
                <input type="checkbox" id="test2">
                <label for="test2">在YouTube视频页面点击插件，能正确显示视频信息</label>
            </div>
            <div class="step">
                <input type="checkbox" id="test3">
                <label for="test3">能够获取并显示可用字幕列表</label>
            </div>
            <div class="step">
                <input type="checkbox" id="test4">
                <label for="test4">能够选择字幕语言</label>
            </div>
            <div class="step">
                <input type="checkbox" id="test5">
                <label for="test5">能够选择下载格式（SRT/VTT/TXT）</label>
            </div>
            <div class="step">
                <input type="checkbox" id="test6">
                <label for="test6">点击下载按钮能成功下载字幕文件</label>
            </div>
            <div class="step">
                <input type="checkbox" id="test7">
                <label for="test7">点击复制按钮能成功复制字幕内容到剪贴板</label>
            </div>
            <div class="step">
                <input type="checkbox" id="test8">
                <label for="test8">在浏览器控制台能看到字幕内容的调试信息</label>
            </div>
            <div class="step">
                <input type="checkbox" id="test9">
                <label for="test9">在非YouTube页面显示正确的错误提示</label>
            </div>
            <div class="step">
                <input type="checkbox" id="test10">
                <label for="test10">批量处理标签页正常切换</label>
            </div>
            <div class="step">
                <input type="checkbox" id="test11">
                <label for="test11">能够输入多个YouTube URL并解析</label>
            </div>
            <div class="step">
                <input type="checkbox" id="test12">
                <label for="test12">批量下载功能正常工作</label>
            </div>
            <div class="step">
                <input type="checkbox" id="test13">
                <label for="test13">批量复制功能正常工作</label>
            </div>
            <div class="step">
                <input type="checkbox" id="test14">
                <label for="test14">进度条和结果显示正常</label>
            </div>
        </div>

        <div class="test-section">
            <h3>🔧 调试方法</h3>
            <div class="step">
                <strong>调试popup界面：</strong> 右键点击插件图标 → "检查弹出式窗口"
            </div>
            <div class="step">
                <strong>调试content script：</strong> 在YouTube页面按F12 → Console查看日志
            </div>
            <div class="step">
                <strong>调试background script：</strong> chrome://extensions/ → 点击"检查视图 service worker"
            </div>
        </div>

        <div class="note">
            <strong>注意：</strong> 如果插件功能异常，请检查浏览器控制台的错误信息，并参考操作.md文档进行故障排除。
        </div>

        <div class="test-section">
            <h3>📁 项目文件结构</h3>
            <pre style="background-color: #f8f9fa; padding: 15px; border-radius: 5px; overflow-x: auto;">
youtube-subtitle/
├── manifest.json          # Chrome插件配置
├── popup.html             # 插件弹窗界面
├── popup.js               # 弹窗逻辑
├── content.js             # 内容脚本
├── background.js          # 后台服务
├── styles.css             # 样式文件
├── icons/                 # 图标文件夹
│   └── icon.svg          # SVG图标
├── project.md             # 项目文档
├── 操作.md                # 操作说明
├── test.html              # 测试页面（本页面）
└── README.md              # 项目说明
            </pre>
        </div>
    </div>

    <script>
        // 简单的测试进度跟踪
        document.querySelectorAll('input[type="checkbox"]').forEach(checkbox => {
            checkbox.addEventListener('change', function() {
                const completed = document.querySelectorAll('input[type="checkbox"]:checked').length;
                const total = document.querySelectorAll('input[type="checkbox"]').length;
                console.log(`测试进度: ${completed}/${total}`);

                if (completed === total) {
                    alert('🎉 恭喜！所有测试项目都已完成！');
                }
            });
        });
    </script>
</body>
</html>
