// content.js - 内容脚本，在YouTube页面运行
class YouTubeContentScript {
  constructor() {
    this.init()
  }

  init() {
    // 监听来自popup的消息
    chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
      if (request.action === "getVideoInfo") {
        this.getVideoInfo().then(sendResponse)
        return true // 保持消息通道开放
      } else if (request.action === "getSearchPageVideos") {
        this.getSearchPageVideos().then(sendResponse)
        return true
      }
    })
  }

  async getVideoInfo() {
    try {
      // 从URL获取视频ID
      const videoId = this.extractVideoId()
      if (!videoId) {
        return { success: false, error: "无法获取视频ID" }
      }

      // 获取视频标题
      const title = this.getVideoTitle()

      return {
        success: true,
        data: {
          videoId: videoId,
          title: title,
          url: window.location.href,
        },
      }
    } catch (error) {
      return { success: false, error: error.message }
    }
  }

  extractVideoId() {
    const urlParams = new URLSearchParams(window.location.search)
    return urlParams.get("v")
  }

  getVideoTitle() {
    // 尝试多种方式获取视频标题
    let title = ""

    // 方法1: 从页面标题获取
    const pageTitle = document.title
    if (pageTitle && pageTitle !== "YouTube") {
      title = pageTitle.replace(" - YouTube", "")
    }

    // 方法2: 从meta标签获取
    if (!title) {
      const metaTitle = document.querySelector('meta[property="og:title"]')
      if (metaTitle) {
        title = metaTitle.content
      }
    }

    // 方法3: 从h1标签获取
    if (!title) {
      const h1Element = document.querySelector(
        "h1.ytd-video-primary-info-renderer"
      )
      if (h1Element) {
        title = h1Element.textContent.trim()
      }
    }

    // 方法4: 从新版YouTube布局获取
    if (!title) {
      const titleElement = document.querySelector(
        "h1.ytd-watch-metadata yt-formatted-string"
      )
      if (titleElement) {
        title = titleElement.textContent.trim()
      }
    }

    return title || "未知标题"
  }

  // 获取视频时长（可选功能）
  getVideoDuration() {
    const durationElement = document.querySelector(".ytp-time-duration")
    return durationElement ? durationElement.textContent : null
  }

  async getSearchPageVideos() {
    try {
      // 检查是否在YouTube搜索页面或首页
      if (!this.isYouTubeSearchOrHomePage()) {
        return { success: false, error: "当前页面不是YouTube搜索页面或首页" }
      }

      // 获取所有视频链接
      const videoLinks = this.extractVideoLinks()

      if (videoLinks.length === 0) {
        return { success: false, error: "未找到视频链接" }
      }

      return {
        success: true,
        videos: videoLinks,
        count: videoLinks.length,
      }
    } catch (error) {
      return { success: false, error: error.message }
    }
  }

  isYouTubeSearchOrHomePage() {
    const url = window.location.href
    return (
      url.includes("youtube.com/results") ||
      (url.includes("youtube.com/") && !url.includes("/watch"))
    )
  }

  extractVideoLinks() {
    const videoLinks = []
    const selectors = [
      // 搜索结果页面的视频链接
      'a#thumbnail[href*="/watch?v="]',
      // 首页推荐视频
      'a#video-title-link[href*="/watch?v="]',
      // 其他可能的视频链接选择器
      'a[href*="/watch?v="]',
    ]

    const processedIds = new Set()

    selectors.forEach((selector) => {
      const elements = document.querySelectorAll(selector)
      elements.forEach((element) => {
        const href = element.getAttribute("href")
        if (href) {
          // 提取视频ID
          const videoId = this.extractVideoIdFromHref(href)
          if (videoId && !processedIds.has(videoId)) {
            processedIds.add(videoId)
            const fullUrl = href.startsWith("http")
              ? href
              : `https://www.youtube.com${href}`
            videoLinks.push({
              videoId: videoId,
              url: fullUrl,
              title: this.getVideoTitle(element),
            })
          }
        }
      })
    })

    return videoLinks
  }

  extractVideoIdFromHref(href) {
    const match = href.match(/[?&]v=([^&]+)/)
    return match ? match[1] : null
  }

  getVideoTitle(element) {
    // 尝试从不同位置获取视频标题
    let title = ""

    // 方法1: 从aria-label获取
    title = element.getAttribute("aria-label")
    if (title) return title

    // 方法2: 从title属性获取
    title = element.getAttribute("title")
    if (title) return title

    // 方法3: 从相邻的标题元素获取
    const titleElement =
      element.querySelector("#video-title") ||
      element.querySelector(".ytd-video-meta-block") ||
      element.closest("ytd-video-renderer")?.querySelector("#video-title")

    if (titleElement) {
      title = titleElement.textContent?.trim()
      if (title) return title
    }

    return "未知标题"
  }

  // 检查视频是否加载完成
  isVideoLoaded() {
    const video = document.querySelector("video")
    return video && video.readyState >= 2
  }
}

// 等待页面加载完成后初始化
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        new YouTubeContentScript();
    });
} else {
    new YouTubeContentScript();
}
