// background.js - 后台服务工作者
class YouTubeSubtitleService {
  constructor() {
    this.init()
  }

  init() {
    // 监听来自popup的消息
    chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
      if (request.action === "getSubtitles") {
        this.getSubtitles(request.videoId).then(sendResponse)
        return true
      } else if (request.action === "downloadSubtitle") {
        this.downloadSubtitle(request).then(sendResponse)
        return true
      } else if (request.action === "getSubtitleContent") {
        this.getSubtitleContent(request).then(sendResponse)
        return true
      } else if (request.action === "downloadCombinedSubtitles") {
        this.downloadCombinedSubtitles(request).then(sendResponse)
        return true
      }
    })
  }

  async getSubtitles(videoId) {
    try {
      // 使用YouTube的内部API获取字幕信息
      const response = await this.fetchSubtitlesList(videoId)

      if (response.success) {
        return {
          success: true,
          subtitles: response.subtitles,
        }
      } else {
        return {
          success: false,
          error: response.error || "获取字幕列表失败",
        }
      }
    } catch (error) {
      return {
        success: false,
        error: "网络请求失败：" + error.message,
      }
    }
  }

  async fetchSubtitlesList(videoId) {
    try {
      // 使用youtube-transcript-api的方法：获取HTML → 提取API密钥 → 调用内部API
      const html = await this.fetchVideoHTML(videoId)
      const apiKey = this.extractInnertubeApiKey(html, videoId)
      const innertubeData = await this.fetchInnertubeData(videoId, apiKey)
      const captionsJson = this.extractCaptionsJson(innertubeData, videoId)

      const subtitles = this.buildSubtitlesList(captionsJson)
      return { success: true, subtitles }
    } catch (error) {
      console.error("获取字幕列表失败:", error)
      // 降级到原有方法
      try {
        const watchUrl = `https://www.youtube.com/watch?v=${videoId}`
        const response = await fetch(watchUrl)
        const html = await response.text()
        const subtitles = this.parseSubtitlesFromHTML(html)

        if (subtitles.length > 0) {
          return { success: true, subtitles }
        }
      } catch (fallbackError) {
        console.error("降级方法也失败:", fallbackError)
      }

      return { success: false, error: error.message }
    }
  }

  parseSubtitlesFromHTML(html) {
    const subtitles = []

    try {
      // 查找字幕配置信息
      const captionsRegex = /"captionTracks":\s*(\[.*?\])/
      const match = html.match(captionsRegex)

      if (match) {
        const captionTracks = JSON.parse(match[1])

        captionTracks.forEach((track) => {
          subtitles.push({
            name: track.name?.simpleText || track.languageCode,
            languageCode: track.languageCode,
            url: track.baseUrl,
            kind: track.kind || "captions",
          })
        })
      }
    } catch (error) {
      console.error("解析字幕信息失败:", error)
    }

    return subtitles
  }

  // 新增方法：获取视频HTML页面
  async fetchVideoHTML(videoId) {
    const watchUrl = `https://www.youtube.com/watch?v=${videoId}`
    const response = await fetch(watchUrl, {
      headers: {
        "Accept-Language": "en-US,en;q=0.9",
        "User-Agent":
          "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
      },
    })

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`)
    }

    return await response.text()
  }

  // 新增方法：从HTML中提取Innertube API密钥
  extractInnertubeApiKey(html, videoId) {
    const pattern = /"INNERTUBE_API_KEY":\s*"([a-zA-Z0-9_-]+)"/
    const match = html.match(pattern)

    if (match && match[1]) {
      return match[1]
    }

    if (html.includes('class="g-recaptcha"')) {
      throw new Error("IP被阻止，需要验证码")
    }

    throw new Error("无法解析YouTube数据，找不到API密钥")
  }

  // 新增方法：调用YouTube内部API
  async fetchInnertubeData(videoId, apiKey) {
    const url = `https://www.youtube.com/youtubei/v1/player?key=${apiKey}`
    const payload = {
      context: {
        client: {
          clientName: "ANDROID",
          clientVersion: "20.10.38",
        },
      },
      videoId: videoId,
    }

    const response = await fetch(url, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "Accept-Language": "en-US,en;q=0.9",
        "User-Agent":
          "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
      },
      body: JSON.stringify(payload),
    })

    if (!response.ok) {
      throw new Error(
        `YouTube API请求失败: ${response.status} ${response.statusText}`
      )
    }

    return await response.json()
  }

  // 新增方法：从Innertube数据中提取字幕信息
  extractCaptionsJson(innertubeData, videoId) {
    // 检查播放状态
    const playabilityStatus = innertubeData.playabilityStatus
    if (!playabilityStatus) {
      throw new Error("无法获取视频播放状态")
    }

    if (playabilityStatus.status !== "OK") {
      const reason = playabilityStatus.reason || "未知错误"
      if (playabilityStatus.status === "LOGIN_REQUIRED") {
        throw new Error("需要登录才能访问此视频")
      } else if (playabilityStatus.status === "ERROR") {
        throw new Error(`视频不可用: ${reason}`)
      } else {
        throw new Error(`视频播放失败: ${reason}`)
      }
    }

    // 提取字幕数据
    const captions = innertubeData.captions
    if (!captions || !captions.playerCaptionsTracklistRenderer) {
      throw new Error("此视频没有可用的字幕")
    }

    const captionsJson = captions.playerCaptionsTracklistRenderer
    if (
      !captionsJson.captionTracks ||
      captionsJson.captionTracks.length === 0
    ) {
      throw new Error("此视频没有可用的字幕轨道")
    }

    return captionsJson
  }

  // 新增方法：构建字幕列表
  buildSubtitlesList(captionsJson) {
    const subtitles = []

    for (const caption of captionsJson.captionTracks) {
      const subtitle = {
        name: caption.name?.runs?.[0]?.text || caption.languageCode,
        languageCode: caption.languageCode,
        url: caption.baseUrl.replace("&fmt=srv3", ""), // 移除srv3格式参数
        kind: caption.kind || "captions",
        isGenerated: caption.kind === "asr", // ASR表示自动生成
      }

      subtitles.push(subtitle)
    }

    return subtitles
  }

  async fetchSubtitlesFromAPI(videoId) {
    // 备用方法：使用第三方API或其他方式
    // 这里可以集成其他字幕获取服务
    return { success: false, error: "暂不支持此视频的字幕获取" }
  }

  async getSubtitleContent(request) {
    try {
      const { subtitle, format } = request

      // 获取字幕内容
      const subtitleContent = await this.fetchSubtitleContent(subtitle.url)

      if (!subtitleContent) {
        return { success: false, error: "获取字幕内容失败" }
      }

      console.log("原始字幕内容长度:", subtitleContent.length)
      console.log("原始字幕内容预览:", subtitleContent.substring(0, 300))

      // 转换格式
      const convertedContent = this.convertSubtitleFormat(
        subtitleContent,
        format
      )

      console.log("转换后字幕内容长度:", convertedContent.length)
      console.log("转换后字幕内容预览:", convertedContent.substring(0, 300))

      return { success: true, content: convertedContent }
    } catch (error) {
      console.error("获取字幕内容失败:", error)
      return { success: false, error: error.message }
    }
  }

  async downloadSubtitle(request) {
    try {
      const { subtitle, videoId, videoTitle, format } = request

      // 获取字幕内容
      const subtitleContent = await this.fetchSubtitleContent(subtitle.url)

      if (!subtitleContent) {
        return { success: false, error: "获取字幕内容失败" }
      }

      // 转换格式
      const convertedContent = this.convertSubtitleFormat(
        subtitleContent,
        format
      )

      // 生成文件名
      const fileName = this.generateFileName(
        videoTitle,
        subtitle.languageCode,
        format
      )

      // 下载文件
      await this.downloadFile(convertedContent, fileName, true)

      return { success: true }
    } catch (error) {
      return { success: false, error: error.message }
    }
  }

  async fetchSubtitleContent(url) {
    try {
      const response = await fetch(url)
      return await response.text()
    } catch (error) {
      throw new Error("获取字幕内容失败：" + error.message)
    }
  }

  convertSubtitleFormat(content, targetFormat) {
    // 检测原始内容格式
    if (content.includes("<?xml") && content.includes("<transcript>")) {
      // XML格式字幕
      return this.convertFromXml(content, targetFormat)
    } else if (content.includes("WEBVTT")) {
      // VTT格式字幕
      return this.convertFromVtt(content, targetFormat)
    } else {
      // 尝试作为VTT处理
      return this.convertFromVtt(content, targetFormat)
    }
  }

  convertFromXml(xmlContent, targetFormat) {
    try {
      // 使用正则表达式解析XML字幕（因为service worker中没有DOMParser）
      const textElements = this.parseXmlText(xmlContent)

      switch (targetFormat) {
        case "srt":
          return this.xmlToSrt(textElements)
        case "vtt":
          return this.xmlToVtt(textElements)
        case "txt":
          return this.xmlToTxt(textElements)
        default:
          return this.xmlToSrt(textElements)
      }
    } catch (error) {
      console.error("XML解析失败:", error)
      return xmlContent
    }
  }

  parseXmlText(xmlContent) {
    const textElements = []
    // 使用正则表达式匹配<text>标签
    const textRegex =
      /<text\s+start="([^"]+)"\s+dur="([^"]+)"[^>]*>([^<]*)<\/text>/g
    let match

    while ((match = textRegex.exec(xmlContent)) !== null) {
      textElements.push({
        start: match[1],
        dur: match[2],
        text: match[3],
      })
    }

    return textElements
  }

  convertFromVtt(vttContent, targetFormat) {
    switch (targetFormat) {
      case "srt":
        return this.vttToSrt(vttContent)
      case "txt":
        return this.vttToTxt(vttContent)
      case "vtt":
      default:
        return vttContent
    }
  }

  xmlToSrt(textElements) {
    let srtContent = ""
    let counter = 1

    for (const element of textElements) {
      const start = parseFloat(element.start)
      const dur = parseFloat(element.dur)
      const end = start + dur
      const text = this.decodeHtmlEntities(element.text.trim())

      if (text) {
        const startTime = this.secondsToSrtTime(start)
        const endTime = this.secondsToSrtTime(end)

        srtContent += `${counter}\n${startTime} --> ${endTime}\n${text}\n\n`
        counter++
      }
    }

    return srtContent
  }

  xmlToVtt(textElements) {
    let vttContent = "WEBVTT\n\n"

    for (const element of textElements) {
      const start = parseFloat(element.start)
      const dur = parseFloat(element.dur)
      const end = start + dur
      const text = this.decodeHtmlEntities(element.text.trim())

      if (text) {
        const startTime = this.secondsToVttTime(start)
        const endTime = this.secondsToVttTime(end)

        vttContent += `${startTime} --> ${endTime}\n${text}\n\n`
      }
    }

    return vttContent
  }

  xmlToTxt(textElements) {
    let txtContent = ""

    for (const element of textElements) {
      const text = this.decodeHtmlEntities(element.text.trim())
      if (text) {
        txtContent += text + "\n"
      }
    }

    return txtContent
  }

  secondsToSrtTime(seconds) {
    const hours = Math.floor(seconds / 3600)
    const minutes = Math.floor((seconds % 3600) / 60)
    const secs = Math.floor(seconds % 60)
    const ms = Math.floor((seconds % 1) * 1000)

    return `${hours.toString().padStart(2, "0")}:${minutes
      .toString()
      .padStart(2, "0")}:${secs.toString().padStart(2, "0")},${ms
      .toString()
      .padStart(3, "0")}`
  }

  secondsToVttTime(seconds) {
    const hours = Math.floor(seconds / 3600)
    const minutes = Math.floor((seconds % 3600) / 60)
    const secs = Math.floor(seconds % 60)
    const ms = Math.floor((seconds % 1) * 1000)

    return `${hours.toString().padStart(2, "0")}:${minutes
      .toString()
      .padStart(2, "0")}:${secs.toString().padStart(2, "0")}.${ms
      .toString()
      .padStart(3, "0")}`
  }

  decodeHtmlEntities(text) {
    // 在service worker中不能使用document，所以手动解码常见的HTML实体
    const entityMap = {
      "&amp;": "&",
      "&lt;": "<",
      "&gt;": ">",
      "&quot;": '"',
      "&#39;": "'",
      "&#x27;": "'",
      "&#x2F;": "/",
      "&#x60;": "`",
      "&#x3D;": "=",
      "&nbsp;": " ",
      "&apos;": "'",
      "&lsquo;": "'",
      "&rsquo;": "'",
      "&ldquo;": '"',
      "&rdquo;": '"',
      "&ndash;": "–",
      "&mdash;": "—",
    }

    return text.replace(/&[#\w]+;/g, (entity) => {
      return entityMap[entity] || entity
    })
  }

  vttToSrt(vttContent) {
    let srtContent = ""
    let counter = 1

    // 移除VTT头部
    const lines = vttContent.split("\n")
    let startIndex = 0

    for (let i = 0; i < lines.length; i++) {
      if (lines[i].includes("-->")) {
        startIndex = i
        break
      }
    }

    for (let i = startIndex; i < lines.length; i++) {
      const line = lines[i].trim()

      if (line.includes("-->")) {
        // 时间轴行
        const timeRange = line.replace(/\./g, ",") // SRT使用逗号而不是点
        srtContent += `${counter}\n${timeRange}\n`

        // 获取字幕文本
        let textLines = []
        for (let j = i + 1; j < lines.length && lines[j].trim() !== ""; j++) {
          if (!lines[j].includes("-->")) {
            textLines.push(lines[j].trim())
          }
        }

        srtContent += textLines.join("\n") + "\n\n"
        counter++
      }
    }

    return srtContent
  }

  vttToTxt(vttContent) {
    const lines = vttContent.split("\n")
    let txtContent = ""

    for (const line of lines) {
      const trimmedLine = line.trim()
      // 跳过时间轴和空行
      if (
        trimmedLine &&
        !trimmedLine.includes("-->") &&
        !trimmedLine.startsWith("WEBVTT")
      ) {
        txtContent += trimmedLine + "\n"
      }
    }

    return txtContent
  }

  generateFileName(videoTitle, languageCode, format) {
    // 清理文件名中的非法字符
    const cleanTitle = videoTitle.replace(/[<>:"/\\|?*]/g, "_").substring(0, 50)
    return `${cleanTitle}_${languageCode}.${format}`
  }

  async downloadCombinedSubtitles(request) {
    try {
      const { content, format, count } = request

      // 转换格式（如果需要）
      let convertedContent = content
      if (format !== "txt") {
        // 对于SRT和VTT格式，需要特殊处理合并内容
        convertedContent = this.convertCombinedContent(content, format)
      }

      // 生成文件名
      const timestamp = new Date()
        .toISOString()
        .slice(0, 19)
        .replace(/[:-]/g, "")
      const fileName = `youtube_subtitles_${count}videos_${timestamp}.${format}`

      // 下载文件
      await this.downloadFile(convertedContent, fileName, false)

      return { success: true }
    } catch (error) {
      return { success: false, error: error.message }
    }
  }

  convertCombinedContent(content, format) {
    // 对于合并的字幕内容，如果是SRT或VTT格式，保持原样
    // 因为内容已经是文本格式，适合直接保存
    return content
  }

  async downloadFile(content, fileName, saveAs = true) {
    try {
      // 直接使用UTF-8编码的data URL，不需要额外编码
      const utf8Content = encodeURIComponent(content)
      const dataUrl = `data:text/plain;charset=utf-8,${utf8Content}`

      await chrome.downloads.download({
        url: dataUrl,
        filename: fileName,
        saveAs: saveAs, // 单个下载显示对话框，批量下载不显示
      })
    } catch (error) {
      console.error("下载文件失败:", error)
      throw new Error("文件下载失败: " + error.message)
    }
  }
}

// 初始化服务
new YouTubeSubtitleService()
