body {
    width: 400px;
    min-height: 450px;
    margin: 0;
    padding: 0;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f5f5f5;
}

.container {
    display: flex;
    flex-direction: column;
    height: 100%;
}

.header {
    background: linear-gradient(135deg, #ff0000, #cc0000);
    color: white;
    padding: 15px;
    text-align: center;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.header h2 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
}

.content {
    flex: 1;
    padding: 15px;
}

.status {
    text-align: center;
    padding: 10px;
    background-color: #e3f2fd;
    border-radius: 5px;
    margin-bottom: 15px;
    border-left: 4px solid #2196f3;
}

.video-info {
    background-color: white;
    padding: 12px;
    border-radius: 5px;
    margin-bottom: 15px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.video-title, .video-id {
    margin-bottom: 8px;
    font-size: 12px;
}

.video-title strong, .video-id strong {
    color: #333;
}

.subtitles-list {
    background-color: white;
    padding: 12px;
    border-radius: 5px;
    margin-bottom: 15px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.subtitles-list h3 {
    margin: 0 0 10px 0;
    font-size: 14px;
    color: #333;
}

.subtitle-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px;
    margin-bottom: 5px;
    background-color: #f8f9fa;
    border-radius: 3px;
    cursor: pointer;
    transition: background-color 0.2s;
}

.subtitle-item:hover {
    background-color: #e9ecef;
}

.subtitle-item.selected {
    background-color: #e3f2fd;
    border: 1px solid #2196f3;
}

.subtitle-lang {
    font-weight: 500;
    color: #333;
}

.subtitle-type {
    font-size: 11px;
    color: #666;
    background-color: #e0e0e0;
    padding: 2px 6px;
    border-radius: 10px;
}

.download-section {
    background-color: white;
    padding: 12px;
    border-radius: 5px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.format-selection {
    margin-bottom: 12px;
}

.format-selection label {
    display: block;
    margin-bottom: 5px;
    font-size: 12px;
    font-weight: 500;
    color: #333;
}

.format-selection select {
    width: 100%;
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 3px;
    font-size: 12px;
}

.action-buttons {
    display: flex;
    gap: 8px;
}

.download-btn, .copy-btn {
    flex: 1;
    padding: 10px;
    border: none;
    border-radius: 5px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: background 0.3s;
}

.download-btn {
    background: linear-gradient(135deg, #4caf50, #45a049);
    color: white;
}

.download-btn:hover {
    background: linear-gradient(135deg, #45a049, #3d8b40);
}

.download-btn:disabled {
    background: #cccccc;
    cursor: not-allowed;
}

.copy-btn {
    background: linear-gradient(135deg, #2196f3, #1976d2);
    color: white;
}

.copy-btn:hover {
    background: linear-gradient(135deg, #1976d2, #1565c0);
}

.copy-btn:disabled {
    background: #cccccc;
    cursor: not-allowed;
}

.copy-btn.copied {
    background: linear-gradient(135deg, #4caf50, #45a049);
}

.error-message {
    background-color: #ffebee;
    color: #c62828;
    padding: 10px;
    border-radius: 5px;
    border-left: 4px solid #f44336;
    font-size: 12px;
}

.footer {
    background-color: #e0e0e0;
    padding: 10px;
    text-align: center;
    font-size: 11px;
    color: #666;
}

.footer p {
    margin: 0;
}

.loading {
    display: inline-block;
    width: 12px;
    height: 12px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #2196f3;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-right: 8px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 标签页样式 */
.tabs {
    display: flex;
    background-color: #e0e0e0;
    border-radius: 5px 5px 0 0;
    margin-bottom: 15px;
}

.tab-button {
    flex: 1;
    padding: 10px;
    border: none;
    background-color: transparent;
    cursor: pointer;
    font-size: 12px;
    font-weight: 500;
    color: #666;
    transition: all 0.3s;
}

.tab-button.active {
    background-color: white;
    color: #333;
    border-radius: 5px 5px 0 0;
    box-shadow: 0 -2px 4px rgba(0,0,0,0.1);
}

.tab-button:hover:not(.active) {
    background-color: #f0f0f0;
}

.tab-content {
    animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

/* 批量处理样式 */
.batch-input-section {
    margin-bottom: 15px;
}

.input-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 5px;
}

.batch-input-section label {
    font-size: 12px;
    font-weight: 500;
    color: #333;
    margin: 0;
}

.auto-fill-btn {
    padding: 6px 12px;
    background: linear-gradient(135deg, #ff9800, #f57c00);
    color: white;
    border: none;
    border-radius: 3px;
    font-size: 11px;
    font-weight: 500;
    cursor: pointer;
    transition: background 0.3s;
}

.auto-fill-btn:hover {
    background: linear-gradient(135deg, #f57c00, #ef6c00);
}

.auto-fill-btn:disabled {
    background: #cccccc;
    cursor: not-allowed;
}

.batch-input-section textarea {
    width: 100%;
    height: 80px;
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 3px;
    font-size: 11px;
    font-family: inherit;
    resize: vertical;
    box-sizing: border-box;
}

.batch-options {
    background-color: white;
    padding: 12px;
    border-radius: 5px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    margin-bottom: 15px;
}

.batch-progress {
    background-color: white;
    padding: 12px;
    border-radius: 5px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    margin-bottom: 15px;
}

.progress-bar {
    width: 100%;
    height: 8px;
    background-color: #e0e0e0;
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 8px;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #4caf50, #45a049);
    width: 0%;
    transition: width 0.3s ease;
}

.progress-text {
    font-size: 12px;
    color: #666;
    text-align: center;
}

.batch-results {
    background-color: white;
    padding: 12px;
    border-radius: 5px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    margin-bottom: 15px;
    max-height: 200px;
    overflow-y: auto;
}

.batch-results h3 {
    margin: 0 0 10px 0;
    font-size: 14px;
    color: #333;
}

.result-item {
    padding: 8px;
    margin-bottom: 5px;
    border-radius: 3px;
    font-size: 11px;
    border-left: 3px solid;
}

.result-item.success {
    background-color: #e8f5e8;
    border-left-color: #4caf50;
    color: #2e7d32;
}

.result-item.error {
    background-color: #ffebee;
    border-left-color: #f44336;
    color: #c62828;
}

.result-item.processing {
    background-color: #e3f2fd;
    border-left-color: #2196f3;
    color: #1565c0;
}

.result-title {
    font-weight: 500;
    margin-bottom: 2px;
}

.result-details {
    font-size: 10px;
    opacity: 0.8;
}
