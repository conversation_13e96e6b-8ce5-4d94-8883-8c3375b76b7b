// popup.js - 弹窗逻辑
class YouTubeSubtitleDownloader {
  constructor() {
    this.selectedSubtitle = null
    this.videoData = null
    this.currentMode = "single" // 'single' or 'batch'
    this.batchResults = []
    this.init()
  }

  async init() {
    this.setupEventListeners()
    await this.checkCurrentTab()
  }

  async checkCurrentTab() {
    try {
      const [tab] = await chrome.tabs.query({
        active: true,
        currentWindow: true,
      })

      if (!tab.url.includes("youtube.com/watch")) {
        this.showError("请在YouTube视频页面使用此插件")
        return
      }

      // 向content script发送消息获取视频信息
      chrome.tabs.sendMessage(
        tab.id,
        { action: "getVideoInfo" },
        (response) => {
          if (chrome.runtime.lastError) {
            this.showError("无法连接到页面，请刷新后重试")
            return
          }

          if (response && response.success) {
            this.videoData = response.data
            this.displayVideoInfo()
            this.loadSubtitles()
          } else {
            this.showError(response?.error || "获取视频信息失败")
          }
        }
      )
    } catch (error) {
      this.showError("检测页面时出错：" + error.message)
    }
  }

  displayVideoInfo() {
    if (!this.videoData) return

    document.getElementById("statusText").textContent = "视频检测成功"
    document.getElementById("videoTitle").textContent = this.videoData.title
    document.getElementById("videoId").textContent = this.videoData.videoId
    document.getElementById("videoInfo").style.display = "block"
  }

  async loadSubtitles() {
    if (!this.videoData) return

    try {
      document.getElementById("statusText").innerHTML =
        '<span class="loading"></span>正在获取字幕列表...'

      // 向background script发送请求获取字幕列表
      const response = await chrome.runtime.sendMessage({
        action: "getSubtitles",
        videoId: this.videoData.videoId,
      })

      if (response.success && response.subtitles.length > 0) {
        this.displaySubtitles(response.subtitles)
        document.getElementById("statusText").textContent = "字幕获取成功"
      } else {
        this.showError("未找到可用字幕")
      }
    } catch (error) {
      this.showError("获取字幕列表失败：" + error.message)
    }
  }

  displaySubtitles(subtitles) {
    const container = document.getElementById("subtitlesContainer")
    container.innerHTML = ""

    subtitles.forEach((subtitle, index) => {
      const item = document.createElement("div")
      item.className = "subtitle-item"
      item.dataset.index = index

      item.innerHTML = `
                <span class="subtitle-lang">${subtitle.name}</span>
                <span class="subtitle-type">${
                  subtitle.kind || "captions"
                }</span>
            `

      item.addEventListener("click", () => {
        this.selectSubtitle(subtitle, item)
      })

      container.appendChild(item)
    })

    document.getElementById("subtitlesList").style.display = "block"
  }

  selectSubtitle(subtitle, element) {
    // 移除之前的选择
    document.querySelectorAll(".subtitle-item").forEach((item) => {
      item.classList.remove("selected")
    })

    // 选择当前项
    element.classList.add("selected")
    this.selectedSubtitle = subtitle

    // 显示下载区域
    document.getElementById("downloadSection").style.display = "block"
  }

  setupEventListeners() {
    // 标签页切换
    document.getElementById("singleTab").addEventListener("click", () => {
      this.switchMode("single")
    })

    document.getElementById("batchTab").addEventListener("click", () => {
      this.switchMode("batch")
    })

    // 单个视频模式
    document.getElementById("downloadBtn").addEventListener("click", () => {
      this.downloadSubtitle()
    })

    document.getElementById("copyBtn").addEventListener("click", () => {
      this.copySubtitle()
    })

    // 批量处理模式
    document
      .getElementById("batchDownloadBtn")
      .addEventListener("click", () => {
        this.batchDownload()
      })

    document.getElementById("batchCopyBtn").addEventListener("click", () => {
      this.batchCopy()
    })

    // 自动填充按钮
    document.getElementById("autoFillBtn").addEventListener("click", () => {
      this.autoFillFromCurrentPage()
    })
  }

  switchMode(mode) {
    this.currentMode = mode

    // 更新标签页状态
    document.querySelectorAll(".tab-button").forEach((btn) => {
      btn.classList.remove("active")
    })

    if (mode === "single") {
      document.getElementById("singleTab").classList.add("active")
      document.getElementById("singleMode").style.display = "block"
      document.getElementById("batchMode").style.display = "none"
    } else {
      document.getElementById("batchTab").classList.add("active")
      document.getElementById("singleMode").style.display = "none"
      document.getElementById("batchMode").style.display = "block"
    }
  }

  async downloadSubtitle() {
    if (!this.selectedSubtitle || !this.videoData) {
      this.showError("请先选择字幕")
      return
    }

    const format = document.getElementById("formatSelect").value
    const downloadBtn = document.getElementById("downloadBtn")

    try {
      downloadBtn.disabled = true
      downloadBtn.textContent = "下载中..."

      const response = await chrome.runtime.sendMessage({
        action: "downloadSubtitle",
        subtitle: this.selectedSubtitle,
        videoId: this.videoData.videoId,
        videoTitle: this.videoData.title,
        format: format,
      })

      if (response.success) {
        downloadBtn.textContent = "下载成功"
        setTimeout(() => {
          downloadBtn.textContent = "下载字幕"
          downloadBtn.disabled = false
        }, 2000)
      } else {
        throw new Error(response.error || "下载失败")
      }
    } catch (error) {
      this.showError("下载失败：" + error.message)
      downloadBtn.textContent = "下载字幕"
      downloadBtn.disabled = false
    }
  }

  async copySubtitle() {
    if (!this.selectedSubtitle || !this.videoData) {
      this.showError("请先选择字幕")
      return
    }

    const format = document.getElementById("formatSelect").value
    const copyBtn = document.getElementById("copyBtn")

    try {
      copyBtn.disabled = true
      copyBtn.textContent = "获取中..."

      // 获取字幕内容
      const response = await chrome.runtime.sendMessage({
        action: "getSubtitleContent",
        subtitle: this.selectedSubtitle,
        videoId: this.videoData.videoId,
        format: format,
      })

      if (response.success) {
        // 复制到剪贴板
        await navigator.clipboard.writeText(response.content)

        copyBtn.textContent = "已复制"
        copyBtn.classList.add("copied")

        // 显示调试信息
        console.log("字幕内容长度:", response.content.length)
        console.log("字幕内容预览:", response.content.substring(0, 200) + "...")

        setTimeout(() => {
          copyBtn.textContent = "复制字幕"
          copyBtn.classList.remove("copied")
          copyBtn.disabled = false
        }, 2000)
      } else {
        throw new Error(response.error || "获取字幕内容失败")
      }
    } catch (error) {
      this.showError("复制失败：" + error.message)
      copyBtn.textContent = "复制字幕"
      copyBtn.disabled = false
    }
  }

  async batchDownload() {
    const urls = this.parseVideoUrls()
    if (urls.length === 0) {
      this.showBatchError("请输入有效的YouTube视频URL")
      return
    }

    const format = document.getElementById("batchFormatSelect").value
    await this.processBatch(urls, format, "download")
  }

  async batchCopy() {
    const urls = this.parseVideoUrls()
    if (urls.length === 0) {
      this.showBatchError("请输入有效的YouTube视频URL")
      return
    }

    const format = document.getElementById("batchFormatSelect").value
    await this.processBatch(urls, format, "copy")
  }

  parseVideoUrls() {
    const input = document.getElementById("videoUrls").value.trim()
    if (!input) return []

    const urls = input
      .split(",")
      .map((url) => url.trim())
      .filter((url) => url)
    const validUrls = []

    for (const url of urls) {
      const videoId = this.extractVideoIdFromUrl(url)
      if (videoId) {
        validUrls.push({ url, videoId })
      }
    }

    return validUrls
  }

  extractVideoIdFromUrl(url) {
    const patterns = [
      /(?:youtube\.com\/watch\?v=|youtu\.be\/|youtube\.com\/embed\/)([^&\n?#]+)/,
      /^([a-zA-Z0-9_-]{11})$/, // 直接的视频ID
    ]

    for (const pattern of patterns) {
      const match = url.match(pattern)
      if (match) return match[1]
    }

    return null
  }

  async processBatch(videos, format, action) {
    this.batchResults = []
    this.showBatchProgress(0, videos.length)
    this.clearBatchResults()

    const batchDownloadBtn = document.getElementById("batchDownloadBtn")
    const batchCopyBtn = document.getElementById("batchCopyBtn")

    batchDownloadBtn.disabled = true
    batchCopyBtn.disabled = true

    let allSubtitles = []

    for (let i = 0; i < videos.length; i++) {
      const video = videos[i]
      this.updateBatchProgress(
        i,
        videos.length,
        `处理视频 ${i + 1}/${videos.length}`
      )

      try {
        // 添加处理中的结果项
        this.addBatchResult(video.videoId, "处理中...", "processing")

        // 获取字幕
        const subtitle = await this.getBatchSubtitle(video.videoId)

        if (subtitle) {
          allSubtitles.push({
            videoId: video.videoId,
            content: subtitle,
          })

          this.updateBatchResult(video.videoId, "成功获取字幕", "success")
        } else {
          this.updateBatchResult(video.videoId, "未找到英文字幕", "error")
        }
      } catch (error) {
        this.updateBatchResult(video.videoId, `错误: ${error.message}`, "error")
      }

      // 添加延迟避免请求过快
      if (i < videos.length - 1) {
        await this.delay(1000)
      }
    }

    // 处理获取到的字幕
    if (allSubtitles.length > 0) {
      const combinedContent = allSubtitles
        .map((item) => `=== 视频ID: ${item.videoId} ===\n${item.content}\n`)
        .join("\n")

      if (action === "copy") {
        try {
          await navigator.clipboard.writeText(combinedContent)
          this.showBatchSuccess(
            `已复制 ${allSubtitles.length} 个视频的字幕到剪贴板`
          )
        } catch (error) {
          this.showBatchError("复制到剪贴板失败: " + error.message)
        }
      } else if (action === "download") {
        try {
          await this.downloadCombinedSubtitles(
            combinedContent,
            format,
            allSubtitles.length
          )
          this.showBatchSuccess(
            `已下载 ${allSubtitles.length} 个视频的合并字幕文件`
          )
        } catch (error) {
          this.showBatchError("下载失败: " + error.message)
        }
      }
    } else {
      this.showBatchError("没有成功获取到任何字幕")
    }

    this.updateBatchProgress(videos.length, videos.length, "处理完成")
    batchDownloadBtn.disabled = false
    batchCopyBtn.disabled = false
  }

  async getBatchSubtitle(videoId) {
    try {
      // 获取字幕列表
      const response = await chrome.runtime.sendMessage({
        action: "getSubtitles",
        videoId: videoId,
      })

      if (!response.success || !response.subtitles.length) {
        return null
      }

      // 查找英文字幕（优先级：English > en > 第一个可用的）
      let englishSubtitle = response.subtitles.find(
        (sub) =>
          sub.languageCode === "en" ||
          sub.name.toLowerCase().includes("english") ||
          sub.languageCode === "en-US" ||
          sub.languageCode === "en-GB"
      )

      if (!englishSubtitle) {
        englishSubtitle = response.subtitles[0] // 使用第一个可用字幕
      }

      // 获取字幕内容
      const contentResponse = await chrome.runtime.sendMessage({
        action: "getSubtitleContent",
        subtitle: englishSubtitle,
        format: "txt", // 统一获取文本格式
      })

      return contentResponse.success ? contentResponse.content : null
    } catch (error) {
      console.error("获取批量字幕失败:", error)
      return null
    }
  }

  async downloadCombinedSubtitles(combinedContent, format, count) {
    try {
      const response = await chrome.runtime.sendMessage({
        action: "downloadCombinedSubtitles",
        content: combinedContent,
        format: format,
        count: count,
      })

      if (!response.success) {
        throw new Error(response.error || "下载失败")
      }
    } catch (error) {
      throw error
    }
  }

  showBatchProgress(current, total) {
    document.getElementById("batchProgress").style.display = "block"
    document.getElementById("batchResults").style.display = "block"
  }

  updateBatchProgress(current, total, text) {
    const percentage = total > 0 ? (current / total) * 100 : 0
    document.getElementById("progressFill").style.width = `${percentage}%`
    document.getElementById("progressText").textContent =
      text || `${current}/${total}`
  }

  addBatchResult(videoId, message, type) {
    const container = document.getElementById("resultsContainer")
    const item = document.createElement("div")
    item.className = `result-item ${type}`
    item.id = `result-${videoId}`
    item.innerHTML = `
      <div class="result-title">视频ID: ${videoId}</div>
      <div class="result-details">${message}</div>
    `
    container.appendChild(item)
  }

  updateBatchResult(videoId, message, type) {
    const item = document.getElementById(`result-${videoId}`)
    if (item) {
      item.className = `result-item ${type}`
      item.querySelector(".result-details").textContent = message
    }
  }

  clearBatchResults() {
    document.getElementById("resultsContainer").innerHTML = ""
  }

  showBatchError(message) {
    document.getElementById("batchErrorText").textContent = message
    document.getElementById("batchErrorMessage").style.display = "block"
    setTimeout(() => {
      document.getElementById("batchErrorMessage").style.display = "none"
    }, 5000)
  }

  showBatchSuccess(message) {
    // 临时显示成功消息
    const successDiv = document.createElement("div")
    successDiv.className = "success-message"
    successDiv.style.cssText = `
      background-color: #e8f5e8;
      color: #2e7d32;
      padding: 10px;
      border-radius: 5px;
      margin-bottom: 10px;
      border-left: 4px solid #4caf50;
    `
    successDiv.textContent = message

    const container = document.getElementById("batchMode")
    container.insertBefore(successDiv, container.firstChild)

    setTimeout(() => {
      successDiv.remove()
    }, 5000)
  }

  async autoFillFromCurrentPage() {
    const autoFillBtn = document.getElementById("autoFillBtn")

    try {
      autoFillBtn.disabled = true
      autoFillBtn.textContent = "获取中..."

      // 获取当前活动标签页
      const [tab] = await chrome.tabs.query({
        active: true,
        currentWindow: true,
      })

      if (!tab.url.includes("youtube.com")) {
        this.showBatchError("请在YouTube页面使用此功能")
        return
      }

      // 向content script发送消息获取视频链接
      const response = await chrome.tabs.sendMessage(tab.id, {
        action: "getSearchPageVideos",
      })

      if (response && response.success) {
        // 将视频URL填充到文本框
        const urls = response.videos.map((video) => video.url).join(",\n")
        document.getElementById("videoUrls").value = urls

        this.showBatchSuccess(`成功获取 ${response.count} 个视频链接`)

        // 显示获取到的视频信息
        console.log("获取到的视频:", response.videos)
      } else {
        this.showBatchError(response?.error || "获取视频链接失败")
      }
    } catch (error) {
      if (error.message.includes("Could not establish connection")) {
        this.showBatchError("无法连接到页面，请刷新YouTube页面后重试")
      } else {
        this.showBatchError("获取视频链接失败：" + error.message)
      }
    } finally {
      autoFillBtn.textContent = "自动获取当前页面视频"
      autoFillBtn.disabled = false
    }
  }

  delay(ms) {
    return new Promise((resolve) => setTimeout(resolve, ms))
  }

  showError(message) {
    document.getElementById("errorText").textContent = message
    document.getElementById("errorMessage").style.display = "block"
    document.getElementById("status").style.display = "none"
  }
}

// 初始化
document.addEventListener("DOMContentLoaded", () => {
  new YouTubeSubtitleDownloader()
})
